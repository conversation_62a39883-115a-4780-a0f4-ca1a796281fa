import Constants from 'expo-constants';

// Configuración de variables de entorno
export const ENV_CONFIG = {
  API_URL: process.env.API_URL || 'https://51a7-131-196-244-129.ngrok-free.app',
  API_KEY: process.env.API_KEY || 'some-apikey',
  EAS_PROJECT_ID: Constants.expoConfig?.extra?.eas?.projectId || '6a7f92ad-d041-4f5d-ab62-de7f7225fee4',
};

// Para desarrollo, también puedes acceder a las variables desde expo-constants
export const getEnvVar = (key: string): string | undefined => {
  return Constants.expoConfig?.extra?.[key];
};
