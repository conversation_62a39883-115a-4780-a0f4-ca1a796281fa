import { Platform, Text } from "react-native";
import { Loading } from "../../components/Loading";
import { QUERIES } from "../../constants/queries";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Me } from "../../interfaces/me";
import { GradientView } from "../../components/layouts/GradientView";
import * as Notifications from "expo-notifications";
import { NotificationSection } from "../../components/Sections/NotificationSection";
import { useEffect, useState } from "react";
import { usePushNotifications } from "../../hooks/usePushNotifications";

export const NotificationsConfig: React.FC = () => {
  const { data: userData, isLoading, error } = useCachedQuery<Me>(QUERIES.ME);
  const { registerToken, unregisterToken } = usePushNotifications();
  const [deviceToken, setDeviceToken] = useState<string | undefined>();

  useEffect(() => {
    registerForPushNotificationsAsync();
  }, []);

  if (isLoading) return <Loading />;
  if (error) return <Text>Error</Text>;
  if (!userData) return <Text>Sin datos</Text>;

  const { pushTokens } = userData || {};

  //Get device model and version
  const device = Platform.OS === "ios" ? "iOS" : "Android";

  const registerForPushNotificationsAsync = async () => {
    let token;
    if (device === "iOS") {
      await Notifications.requestPermissionsAsync();
    }
    try {
      token = (await Notifications.getExpoPushTokenAsync()).data;
      console.log("token", token);
      setDeviceToken(token);
    } catch (error) {
      console.log(error);
    }
  };

  const tokenEnabled = pushTokens?.some((token) => token.token === deviceToken);

  const handlePushToggle = () => {
    console.log("doing handle");
    console.log("tokenEnabled", tokenEnabled);
    if (tokenEnabled) {
      const tokenToDelete = pushTokens?.find(
        (token) => token.token === deviceToken
      )?.id;
      if (tokenToDelete) {
        unregisterToken.mutate(tokenToDelete);
      }
    } else {
      console.log("deviceToken", deviceToken);
      if (!deviceToken) return;
      registerToken.mutate({ token: deviceToken, device });
    }
  };

  return (
    <GradientView firstLineText="Configuración de Notificaciones">
      <NotificationSection
        isEnabled={tokenEnabled}
        onToggle={() => handlePushToggle()}
        title="Notificaciones push"
        label="Notificaciones push"
      />
      <NotificationSection
        isEnabled={true}
        onToggle={() => console.log("change")}
        title="Notificaciones de texto"
        label="Notificaciones de texto"
      />
      <NotificationSection
        isEnabled={true}
        onToggle={() => console.log("change")}
        title="Notificaciones de correo"
        label="Notificaciones de correo"
      />
    </GradientView>
  );
};
