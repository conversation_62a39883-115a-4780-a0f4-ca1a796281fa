{"expo": {"name": "resapp", "slug": "resapp", "version": "1.0.0", "orientation": "portrait", "icon": "./src/assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "owner": "r<PERSON><PERSON><PERSON><PERSON>", "splash": {"image": "./src/assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.anonymous.resapp"}, "android": {"adaptiveIcon": {"foregroundImage": "./src/assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.anonymous.resapp"}, "web": {"favicon": "./src/assets/favicon.png"}, "extra": {"eas": {"projectId": "6a7f92ad-d041-4f5d-ab62-de7f7225fee4"}}, "plugins": [["expo-image-picker", {"photosPermission": {"title": "<PERSON><PERSON>", "message": "Permítenos acceder a tu galería para seleccionar imágenes.", "buttonPositive": "Ok"}}]]}}